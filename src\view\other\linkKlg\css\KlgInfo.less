:deep(img) {
  max-width: 350px;
  height: auto;
}
:deep(p) {
  margin: 0;
}
.wrapper {
  display: flex;
  flex-direction: column !important;
  width: 100%;
  font-size: 14px;
  .up-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 300px;
    .header {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .header-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        .header-title {
          font-size: 24px;
          font-weight: 600;
          margin-right: 10px;
          :deep(p) {
            margin: 0;
          }
        }
        .header-type {
          white-space: nowrap;
          display: flex;
          height: 20px;
          padding: 0 5px;
          border: 1px solid var(--color-grey);
          color: var(--color-grey);
          border-radius: 3px;
          font-size: 14px;
          min-width: 80px;
          margin-right: 10px;
          bottom: 0;
        }
      }
      .header-right {
        /* display: flex;
        align-items: center; */
        min-width: 70px;
        cursor: pointer;
        .header-right-text {
          white-space: nowrap;
          margin-left: 5px;
          color: var(--color-primary);
          font-size: 12px;
        }
      }
    }
    .base-info {
      width: 100%;
      color: var(--color-deep);
      font-size: 12px;
      .author-info {
        margin-right: 40px;
      }
      /* 保存时间样式 */
      .save-time {
        display: inline-block;
      }
    }
    .detail-info {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      color: var(--color-black);
      font-size: 14px;
      position: relative;
      .detail-info-left {
        width: 100%;
        display: flex;
        flex-direction: column;
        .info-syn {
          width: 90%;
          font-size: 14px;
          font-weight: 600;
          display: flex;
          flex-direction: row;
          .info-syn-text-block {
            width: 100%;
            white-space: normal;
            overflow-wrap: break-word;
            .info-syn-text {
              font-weight: 400;
              white-space: normal;
            }
          }
        }
      }
      .detail-info-right {
        position: absolute;
        bottom: 2px;
        right: 0;
        display: flex;
        align-items: center;
        cursor: pointer;
        .detail-info-right-text {
          color: var(--color-primary);
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
    .main-container {
      .switch {
        display: flex;
        align-items: center;
        justify-content: end;

        .mode-hint {
          margin-left: 10px;
          font-size: 12px;
          color: var(--color-grey);
          font-style: italic;
        }
      }
      // :deep(pre) {
      //   display: flex;
      //   background-color: #f5f5f5; /* 浅灰色背景 */
      //   border: 1px solid #ccc; /* 边框颜色 */
      //   border-radius: 4px; /* 圆角边框 */
      //   padding: 16px; /* 内边距 */
      //   font-size: 14px; /* 字体大小 */
      //   font-family:
      //     'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace; /* 等宽字体 */
      //   overflow-x: auto;
      //   line-height: 1.5; /* 行高 */
      //   color: #333; /* 字体颜色 */
      // }
      .content-container {
        display: flex;
        width: 100%;
        flex-direction: column;
        word-break: break-all;
      }
      .proof-container {
        margin-top: 30px;
        font-size: 14px;
        .proof-container-title {
          color: var(--color-black);
          font-weight: 600;
          margin-bottom: 5px;
        }
        :deep(.proof-block) {
          background-color: var(--color-light);
          border-radius: 3px;
          padding: 10px;
          margin-bottom: 10px;
          .proof-block-item {
            margin-top: 5px;
            display: flex;
            flex-direction: row;
            :deep(p) {
              margin: 0;
            }
            .proof-item-label {
              font-weight: 600;
              margin-right: 20px;
            }
          }
        }
      }
    }
  }
  .down-wrapper {
    width: 100%;
    background-color: white;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    .ref-container-title {
      color: var(--color-black);
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    .ref-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      .ref-line {
        width: 100%;
        padding: 5px 0px;
        display: flex;
        flex-direction: row;
        font-size: 14px;
        .ref-line-name {
          padding: 5px 10px;
          width: 70%;
          background-color: var(--color-light);
        }
        .ref-line-chapter {
          margin-left: 10px;
          padding: 5px 10px;
          width: 30%;
          background-color: var(--color-light);
        }
      }
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
/* :deep(.highlight) {
  color: var(--color-primary);
  cursor: pointer;
} */
:deep(.highlightHover) {
  background-color: #d6e9f6;
  transform: scale(1.05);
  transform-origin: center center;
  transition:
    transform 0.2s ease-in-out,
    background-color 0.2s ease-in-out;
  position: relative;
  z-index: 10;
  display: inline-block;
}

:deep(.highlightHover img) {
  border: 2px solid var(--el-color-primary); /* 添加边框 */
}
/* 省略号文本样式 */
.ellipsis-text-inline {
  color: white;
  display: inline-block;
  max-width: 200px; /* 设置一个明确的最大宽度 */
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
}
/* 对不包含公式的内容应用省略号效果以及公式的垂直居中 */
:deep(.ellipsis-text p),
:deep(.ellipsis-text-inline p) {
  display: inline-block !important;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-height: 100%;
  max-width: 100%; /* 确保p标签不会超出父容器 */
}
.keyword-container {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  max-width: 320px;
}

/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}

.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  img {
    width: 16px;
    height: 16px;
  }
}

.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}

/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
.floating-content {
  padding: 8px 12px;
  background: var(--color-primary);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family:
    '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  overflow: hidden;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;

  .floating-content-item {
    padding: 0;
    transition: all 0.2s ease;
    max-width: 400px;
    &:hover {
      font-weight: 700;
      cursor: pointer;
      transform: translateX(2px);
    }
  }
}
